#!/usr/bin/env python3
"""
Test-time Visual Anchor Embeddings Optimization for ControlMLLM
基于ATPrompt启发的多层次visual anchor优化方法
"""
import sys, os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from PIL import Image
import argparse
from tqdm import tqdm
from transformers import AutoProcessor, LlavaForConditionalGeneration, BitsAndBytesConfig
import torchvision.transforms as transforms
import cv2
import copy

from utils import compute_ca_loss, show_image_relevance
from visualizer import get_local
import clip

# Activate get_local and load CLIP
get_local.activate()
_, preprocess = clip.load("ViT-B/32", device='cpu', jit=False)

def parse_args():
    parser = argparse.ArgumentParser(description="Visual Anchor Embeddings Optimization for ControlMLLM")
    
    # Model and data paths
    parser.add_argument('--model_path', type=str, default="pretrained_models/llava-1.5-7b-hf")
    parser.add_argument('--data_path', type=str, default="data/ROC/LVIS")
    parser.add_argument('--question_file', type=str, default='data/ROC/question_roc.json')
    parser.add_argument('--answers_file', type=str, default='outputs/llava_roc_visual_anchors.json')
    
    # Visual prompt settings
    parser.add_argument('--visual_prompt', type=str, default='Box', choices=['Box', 'Mask', 'Scribble', 'Point'])
    parser.add_argument('--H', type=int, default=24)
    parser.add_argument('--W', type=int, default=24)
    parser.add_argument('--n_px', type=int, default=224)
    
    # Optimization parameters
    parser.add_argument('--T', type=int, default=15, help='Number of optimization steps')
    parser.add_argument('--alpha', type=float, default=400, help='Loss weight')
    parser.add_argument('--lr', type=float, default=0.1, help='Learning rate for anchor optimization (using manual gradient descent like llava_roc.py)')
    parser.add_argument('--beta', type=float, default=0.7, help='EMA weight for anchor updates')
    
    # Visual Anchor settings
    parser.add_argument('--n_spatial_anchors', type=int, default=2, help='Number of spatial anchor embeddings')
    parser.add_argument('--n_semantic_anchors', type=int, default=2, help='Number of semantic anchor embeddings')
    parser.add_argument('--n_relational_anchors', type=int, default=2, help='Number of relational anchor embeddings')
    parser.add_argument('--anchor_init_std', type=float, default=0.01, help='Initialization std for anchors')
    
    # Optimization strategy
    parser.add_argument('--use_ema', action='store_true', help='Use EMA for anchor updates')
    parser.add_argument('--early_stop', action='store_true', help='Enable early stopping')
    parser.add_argument('--loss_threshold', type=float, default=0.001, help='Early stop loss threshold')
    
    # Other settings
    parser.add_argument('--show_att', action='store_true', help='Show attention maps')
    parser.add_argument('--resume', action='store_true', help='Resume from checkpoint')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')
    
    return parser.parse_args()


class VisualAnchorOptimizer(nn.Module):
    """
    Multi-level Visual Anchor Embeddings Optimizer
    包含三个层次的anchors: spatial, semantic, relational
    """
    def __init__(self, embed_dim=4096, n_spatial=2, n_semantic=2, n_relational=2, init_std=0.01):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.n_spatial = n_spatial
        self.n_semantic = n_semantic  
        self.n_relational = n_relational
        
        # Level 1: Spatial Anchors - 学习空间定位和区域关注
        self.spatial_anchors = nn.Parameter(
            torch.randn(n_spatial, embed_dim) * init_std
        )
        
        # Level 2: Semantic Anchors - 学习语义特征理解  
        self.semantic_anchors = nn.Parameter(
            torch.randn(n_semantic, embed_dim) * init_std
        )
        
        # Level 3: Relational Anchors - 学习对象关系比较
        self.relational_anchors = nn.Parameter(
            torch.randn(n_relational, embed_dim) * init_std
        )
        
        self.total_anchors = n_spatial + n_semantic + n_relational
        
    def get_anchor_embeddings(self):
        """返回按顺序排列的所有anchor embeddings"""
        return torch.cat([
            self.spatial_anchors,    # 空间锚点在前
            self.semantic_anchors,   # 语义锚点在中
            self.relational_anchors  # 关系锚点在后
        ], dim=0)
    
    def get_anchor_description(self):
        """返回anchors的描述，用于调试"""
        return {
            'spatial': f"{self.n_spatial} anchors for spatial grounding",
            'semantic': f"{self.n_semantic} anchors for semantic understanding", 
            'relational': f"{self.n_relational} anchors for relational comparison"
        }


# 注意：create_anchored_embeddings函数已被新的token-based方法替代
# 新方法直接修改embedding层的权重，更加兼容


def update_attention_mask(attention_mask, n_anchors):
    """更新attention mask以包含新增的anchor tokens"""
    batch_size, seq_len = attention_mask.shape
    
    # 为anchor tokens添加attention mask (全为1，表示attend to these tokens)
    anchor_mask = torch.ones(batch_size, n_anchors, dtype=attention_mask.dtype, device=attention_mask.device)
    
    # 连接anchor mask和原始mask
    new_attention_mask = torch.cat([anchor_mask, attention_mask], dim=1)
    
    return new_attention_mask


def extract_options_from_question(question_text):
    """提取问题中的选项，用于关系分析"""
    import re
    # 改进的正则表达式，处理 "a bottle or a banana" 这样的情况
    # 匹配 "a/an/the [object1] or a/an/the [object2]" 模式
    match = re.search(r'(?:a\s+|an\s+|the\s+)?(\w+)\s+or\s+(?:a\s+|an\s+|the\s+)?(\w+)', question_text, re.IGNORECASE)
    if match:
        return match.group(1).strip(), match.group(2).strip()
    
    # 如果上面的模式不匹配，尝试更简单的模式
    match = re.search(r'(\w+)\s+or\s+(\w+)', question_text, re.IGNORECASE)
    if match:
        obj1, obj2 = match.group(1).strip(), match.group(2).strip()
        # 过滤掉常见的冠词
        if obj1.lower() in ['a', 'an', 'the']:
            return obj2, None
        if obj2.lower() in ['a', 'an', 'the']:
            return obj1, None
        return obj1, obj2
    
    return None, None


def analyze_object_relationship(opt1, opt2):
    """
    简化的对象关系分析，返回通用的关系类型
    
    Returns:
        relationship_type: 'comparison' for any two objects, 'unrelated' if no objects found
    """
    if not opt1 or not opt2:
        return 'unrelated'
    
    # 简化为通用的comparison关系，让模型自己学习具体的关系模式
    return 'comparison'


def optimize_visual_anchors_for_question(question, model, processor, device, args):
    """
    为单个问题优化visual anchor embeddings
    """
    # 准备图像和问题
    image_path = os.path.join(args.data_path, 'image', 
                             question['image_path'].split('/')[-2], 
                             question['image_path'].split('/')[-1])
    image = Image.open(image_path)
    iw, ih = image.size
    
    original_question = question['text'].replace('<location> ', '')
    prompt = f"USER: <image>\n{original_question} ASSISTANT:"
    
    if args.verbose:
        print(f"Original question: {original_question}")
    
    # 分析问题中的对象关系
    opt1, opt2 = extract_options_from_question(original_question)
    relationship_type = analyze_object_relationship(opt1, opt2)
    
    if args.verbose:
        print(f"Objects: {opt1} vs {opt2}, Relationship: {relationship_type}")
    
    # 处理输入
    inputs = processor(text=prompt, images=image, return_tensors="pt").to(device)
    
    # 获取图像token位置
    img_token_positions = torch.where(inputs['input_ids'] == 32000)
    if len(img_token_positions[0]) > 0:
        img_token_idx = img_token_positions[1][0].item()
    else:
        raise ValueError("No image token found")
    
    # 创建visual mask
    mask = create_visual_mask(question, image, ih, iw, args)
    mask = mask.to(device)
    
    # 记录原始inputs用于后续处理
    original_input_ids = inputs.input_ids.clone()
    original_attention_mask = inputs.attention_mask.clone()
    
    # 获取text embeddings（不处理图像特征，让模型内部处理）
    with torch.no_grad():
        original_inputs_embeds = model.get_input_embeddings()(inputs.input_ids)
    
    # 获取模型的数据类型（适配量化模型）
    model_dtype = original_inputs_embeds.dtype
    print(f"Model embeddings dtype: {model_dtype}")
    
    # 初始化Visual Anchor Optimizer - 改用更稳定的初始化
    anchor_optimizer = VisualAnchorOptimizer(
        embed_dim=original_inputs_embeds.shape[-1],
        n_spatial=args.n_spatial_anchors,
        n_semantic=args.n_semantic_anchors, 
        n_relational=args.n_relational_anchors,
        init_std=args.anchor_init_std
    ).to(device)
    
    # 改进初始化：使用zeros_like风格的初始化，更稳定
    with torch.no_grad():
        for param in anchor_optimizer.parameters():
            param.data = torch.zeros_like(param.data, dtype=model_dtype)
            param.requires_grad_(True)
    
    # 不再使用Adam优化器，改用手动梯度下降（学习llava_roc.py）
    # optimizer = torch.optim.Adam(anchor_optimizer.parameters(), lr=args.lr)
    
    # 初始化EMA历史状态
    anchor_ema_state = copy.deepcopy(anchor_optimizer.state_dict())
    
    if args.verbose:
        print(f"Anchor configuration: {anchor_optimizer.get_anchor_description()}")
        print(f"Total trainable parameters: {sum(p.numel() for p in anchor_optimizer.parameters() if p.requires_grad)}")
        print(f"Using manual gradient descent (lr={args.lr}) like llava_roc.py")
    
    # 检查token ids范围安全性
    vocab_size = model.get_input_embeddings().weight.shape[0]
    anchor_start_id = 32001
    anchor_end_id = anchor_start_id + anchor_optimizer.total_anchors
    
    if anchor_end_id > vocab_size:
        print(f"Warning: Anchor token ids ({anchor_start_id}-{anchor_end_id}) exceed vocab size ({vocab_size})")
        print("Adjusting to use safe token range...")
        anchor_start_id = vocab_size - anchor_optimizer.total_anchors - 1
    
    if args.verbose:
        print(f"Using anchor token ids: {anchor_start_id} to {anchor_start_id + anchor_optimizer.total_anchors - 1}")
    
    # 记录原始输出用于对比
    with torch.no_grad():
        get_local.clear()
        original_outputs = model.generate(
            **inputs,
            max_new_tokens=30,
            return_dict_in_generate=True,
            output_attentions=True,
            pad_token_id=processor.tokenizer.eos_token_id
        )
        original_generated_text = processor.batch_decode(original_outputs.sequences, skip_special_tokens=True)[0]
    
    print(f"Original output: {original_generated_text}")
    
    # 计算初始loss用于对比
    initial_loss = None
    with torch.no_grad():
        # 获取初始attention maps来计算baseline loss
        get_local.clear()
        initial_model_outputs = model(
            **inputs,
            output_attentions=True,
            return_dict=True
        )
        
        # 尝试获取attention maps
        attention_keys = [
            'force_eager_attention_with_decorator.<locals>.decorated_eager_attention_forward', 
            'eager_attention_forward'
        ]
        
        ori_attention_maps = None
        for key in attention_keys:
            if key in get_local.cache:
                ori_attention_maps = get_local.cache[key]
                break
        
        if ori_attention_maps is None and hasattr(initial_model_outputs, 'attentions') and initial_model_outputs.attentions is not None:
            ori_attention_maps = initial_model_outputs.attentions
        
        if ori_attention_maps is not None:
            attention_maps = [att for att in ori_attention_maps if att is not None and att.shape[-2] > 1]
            if len(attention_maps) > 0:
                mean_att = torch.cat([att.to(device) for att in attention_maps], 0).mean(0)
                target2img_rel = mean_att[:, img_token_idx + args.H * args.W:, 
                                        img_token_idx:img_token_idx + args.H * args.W].mean(axis=0).mean(axis=0).unsqueeze(0)
                initial_loss = args.alpha * compute_ca_loss(target2img_rel.to(mask.device), 
                                                          masks=[mask], 
                                                          choice=args.visual_prompt, 
                                                          object_positions=None)
                initial_loss = initial_loss.item()
        
        get_local.clear()
    
    if initial_loss is not None:
        print(f"Initial loss: {initial_loss:.4f}")
    
    # 优化循环
    loss_history = []
    best_loss = float('inf')
    best_anchor_state = None
    # anchor_ema_state = None # This line is now redundant as it's initialized outside
    
    for step in range(args.T):
        # 创建包含anchor tokens的新input_ids
        anchor_token_ids = torch.arange(
            anchor_start_id, anchor_start_id + anchor_optimizer.total_anchors,
            device=device, dtype=torch.long
        ).unsqueeze(0)  # [1, n_anchors]
        
        # 在开头插入anchor token ids
        new_input_ids = torch.cat([
            anchor_token_ids,
            original_input_ids
        ], dim=1)
        
        # 更新attention mask
        new_attention_mask = update_attention_mask(original_attention_mask, anchor_optimizer.total_anchors)
        
        # 更新image token索引 (因为添加了anchor tokens)
        updated_img_token_idx = img_token_idx + anchor_optimizer.total_anchors
        
        # 方法改进：手动构建包含anchor embeddings的inputs_embeds
        # 获取anchor embeddings (保持梯度)
        anchor_embeddings = anchor_optimizer.get_anchor_embeddings().unsqueeze(0)  # [1, n_anchors, embed_dim]
        
        # 获取原始文本的embeddings
        with torch.no_grad():
            original_text_embeds = model.get_input_embeddings()(original_input_ids)
        
        # 确保数据类型一致
        anchor_embeddings = anchor_embeddings.to(original_text_embeds.dtype)
        
        # 拼接anchor embeddings和原始embeddings (保持anchor部分的梯度)
        full_inputs_embeds = torch.cat([
            anchor_embeddings,  # 有梯度的anchor embeddings
            original_text_embeds  # 原始文本embeddings
        ], dim=1)
        
        # 前向传播 - 使用inputs_embeds而不是input_ids
        get_local.clear()
        
        # 改用model()前向传播而不是generate()，以保持梯度连接
        with torch.enable_grad():
            model_outputs = model(
                inputs_embeds=full_inputs_embeds,
                pixel_values=inputs.pixel_values,
                attention_mask=new_attention_mask,
                output_attentions=True,
                return_dict=True
            )
        
        # 生成一些tokens来触发注意力计算 (可选，主要用于获取attention patterns)
        # 注意：这里我们主要关心attention maps，不一定需要完整的生成
        
        # 获取注意力图
        attention_keys = [
            'force_eager_attention_with_decorator.<locals>.decorated_eager_attention_forward', 
            'eager_attention_forward'
        ]
        
        ori_attention_maps = None
        for key in attention_keys:
            if key in get_local.cache:
                ori_attention_maps = get_local.cache[key]
                break
        
        if ori_attention_maps is None:
            print(f"Warning: No attention maps found at step {step}")
            # 如果没有找到attention maps，尝试使用model outputs中的attentions
            if hasattr(model_outputs, 'attentions') and model_outputs.attentions is not None:
                ori_attention_maps = model_outputs.attentions
            else:
                continue
            
        # 处理注意力图
        attention_maps = [att for att in ori_attention_maps if att is not None and att.shape[-2] > 1]
        
        if len(attention_maps) > 0:
            mean_att = torch.cat([att.to(device) for att in attention_maps], 0).mean(0)
            
            # 计算target到image的注意力 (注意索引偏移)
            target2img_rel = mean_att[:, updated_img_token_idx + args.H * args.W:, 
                                    updated_img_token_idx:updated_img_token_idx + args.H * args.W].mean(axis=0).mean(axis=0).unsqueeze(0)
            
            # 检查数值稳定性
            if torch.isnan(target2img_rel).any() or torch.isinf(target2img_rel).any():
                print(f"Warning: NaN/Inf detected in attention maps at step {step}")
                continue
            
            # 计算loss
            loss = args.alpha * compute_ca_loss(target2img_rel.to(mask.device), 
                                              masks=[mask], 
                                              choice=args.visual_prompt, 
                                              object_positions=None)
            
            # 检查loss数值稳定性
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"Warning: NaN/Inf loss detected at step {step}, skipping")
                continue
            
            loss_history.append(loss.item())
            
            # 保存最佳结果
            if loss.item() < best_loss:
                best_loss = loss.item()
                best_anchor_state = copy.deepcopy(anchor_optimizer.state_dict())
            
            if args.verbose:
                print(f"Step {step}: Loss = {loss.item():.4f}")
            
            # 反向传播并更新anchors
            # optimizer.zero_grad() # No longer using Adam
            loss.backward()
            
            # 手动梯度下降更新参数（学习llava_roc.py的方式）
            with torch.no_grad():
                for param in anchor_optimizer.parameters():
                    if param.grad is not None:
                        param.data -= args.lr * param.grad
                        param.grad.zero_()  # 清除梯度
            
            # 确保优化后的参数保持正确的数据类型
            with torch.no_grad():
                for param in anchor_optimizer.parameters():
                    param.data = param.data.to(model_dtype)
                    # 不再需要NaN检查，手动梯度下降更稳定
            
            # EMA更新 (按llava_roc.py的方式)
            current_state = anchor_optimizer.state_dict()
            for key in anchor_ema_state:
                anchor_ema_state[key] = args.beta * current_state[key] + (1 - args.beta) * anchor_ema_state[key]
            
            # Early stopping
            if args.early_stop and len(loss_history) >= 3:
                recent_losses = loss_history[-3:]
                if max(recent_losses) - min(recent_losses) < args.loss_threshold:
                    print(f"Early stopping at step {step}: loss converged")
                    break
        
        get_local.clear()
        torch.cuda.empty_cache()
    
    # 使用最佳anchor state生成最终输出
    if best_anchor_state is not None:
        anchor_optimizer.load_state_dict(best_anchor_state)
    
    # 应用EMA状态 (学习llava_roc.py的方式)
    if args.use_ema:
        anchor_optimizer.load_state_dict(anchor_ema_state)
    
    with torch.no_grad():
        get_local.clear()
        
        # 创建最终的anchor token ids (虽然不直接使用，但保持一致性)
        final_anchor_token_ids = torch.arange(
            anchor_start_id, anchor_start_id + anchor_optimizer.total_anchors,
            device=device, dtype=torch.long
        ).unsqueeze(0)
        
        final_input_ids = torch.cat([
            final_anchor_token_ids,
            original_input_ids
        ], dim=1)
        
        final_attention_mask = update_attention_mask(original_attention_mask, anchor_optimizer.total_anchors)
        
        # 方法改进：手动构建包含anchor embeddings的inputs_embeds
        # 获取anchor embeddings (保持梯度)
        final_anchor_embeddings = anchor_optimizer.get_anchor_embeddings().unsqueeze(0) # [1, n_anchors, embed_dim]
        
        # 获取原始文本的embeddings
        with torch.no_grad():
            final_text_embeds = model.get_input_embeddings()(original_input_ids)
        
        # 确保数据类型一致
        final_anchor_embeddings = final_anchor_embeddings.to(final_text_embeds.dtype)
        
        # 拼接anchor embeddings和原始embeddings (保持anchor部分的梯度)
        full_final_inputs_embeds = torch.cat([
            final_anchor_embeddings,  # 有梯度的anchor embeddings
            final_text_embeds  # 原始文本embeddings
        ], dim=1)
        
        # 使用generate()生成最终的可读输出
        final_outputs = model.generate(
            inputs_embeds=full_final_inputs_embeds,
            pixel_values=inputs.pixel_values,
            attention_mask=final_attention_mask,
            max_new_tokens=30,
            return_dict_in_generate=True,
            output_attentions=True,
            pad_token_id=processor.tokenizer.eos_token_id
        )
        
        optimized_generated_text = processor.batch_decode(final_outputs.sequences, skip_special_tokens=True)[0]
    
    print(f"Optimized output: {optimized_generated_text}")
    print(f"Best loss: {best_loss:.4f}")
    
    # 处理答案格式，确保两个答案都有完整的USER/ASSISTANT格式
    def extract_assistant_response(full_text):
        """提取ASSISTANT后的回答部分"""
        if "ASSISTANT:" in full_text:
            return full_text.split("ASSISTANT:")[-1].strip()
        return full_text.strip()
    
    # 获取用户问题部分（保持一致格式）
    user_question_part = f"USER: <image>\n{original_question} ASSISTANT:"
    
    # 提取原始和优化后的助手回答
    original_assistant_response = extract_assistant_response(original_generated_text)
    optimized_assistant_response = extract_assistant_response(optimized_generated_text)
    
    # 构建完整格式的答案
    formatted_original = f"{user_question_part} {original_assistant_response}"
    formatted_optimized = f"{user_question_part} {optimized_assistant_response}"
    
    return [formatted_original, formatted_optimized], best_loss, relationship_type, initial_loss


def create_visual_mask(question, image, ih, iw, args):
    """创建visual mask (与之前实现相同)"""
    mask = np.zeros((ih, iw))
    
    if args.visual_prompt == 'Box':
        bbox = question['bbox']
        x_min, y_min, x_max, y_max = int(bbox[0]), int(bbox[1]), int(bbox[0] + bbox[2]), int(bbox[1] + bbox[3])
        mask[y_min:y_max, x_min:x_max] = 1
        
    elif args.visual_prompt == 'Mask':
        mask_path = os.path.join(args.data_path, 'mask', question['seg_mask'])
        mask = np.array(Image.open(mask_path))
        
    elif args.visual_prompt == 'Scribble':
        for scri in question['scribble']:
            mask[int(scri[1]), int(scri[0])] = 1
        mask = ((1-mask) * 255).astype(np.uint8)
        distance_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        mask = cv2.normalize(distance_transform, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
    elif args.visual_prompt == 'Point':
        center_point = question['center_point']
        mask[int(center_point[1]), int(center_point[0])] = 1
        mask = ((1-mask) * 255).astype(np.uint8)
        distance_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        mask = cv2.normalize(distance_transform, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
    
    # 转换为tensor
    mask = transforms.Compose([
        transforms.ToPILImage(),
        transforms.Resize(args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
        transforms.CenterCrop(args.n_px),
        transforms.Resize(args.H, interpolation=transforms.InterpolationMode.NEAREST),
        transforms.ToTensor(),
    ])(mask)[0]
    
    return mask


def force_eager_attention_with_decorator(model):
    """强制使用eager attention实现并添加装饰器"""
    replaced = 0
    from transformers.models.llama.modeling_llama import LlamaDecoderLayer, eager_attention_forward
    import transformers.models.llama.modeling_llama as llama_module
    
    # 添加get_local装饰器到eager_attention_forward
    @get_local('attn_weights')
    def decorated_eager_attention_forward(module, query, key, value, attention_mask, scaling, dropout=0.0, **kwargs):
        from transformers.models.llama.modeling_llama import repeat_kv
        import torch.nn.functional as F
        
        key_states = repeat_kv(key, module.num_key_value_groups)
        value_states = repeat_kv(value, module.num_key_value_groups)

        attn_weights = torch.matmul(query, key_states.transpose(2, 3)) * scaling
        if attention_mask is not None:
            causal_mask = attention_mask[:, :, :, : key_states.shape[-2]]
            attn_weights = attn_weights + causal_mask

        attn_weights = F.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query.dtype)
        attn_weights = F.dropout(attn_weights, p=dropout, training=module.training)
        attn_output = torch.matmul(attn_weights, value_states)
        attn_output = attn_output.transpose(1, 2).contiguous()

        return attn_output, attn_weights

    # 替换eager_attention_forward函数
    llama_module.eager_attention_forward = decorated_eager_attention_forward
    
    # 设置eager attention
    model.config._attn_implementation = "eager"
    
    for name, module in model.named_modules():
        if isinstance(module, LlamaDecoderLayer):
            module.self_attn.config._attn_implementation = "eager"
            replaced += 1
    
    print(f"Forced {replaced} layers to use eager attention with decorator")
    return model


def main():
    args = parse_args()
    
    # 设置设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 创建输出目录
    os.makedirs('vis', exist_ok=True) if args.show_att else None
    
    # 加载模型
    print("Loading LLaVA model...")
    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
    )
    
    model = LlavaForConditionalGeneration.from_pretrained(
        args.model_path,
        quantization_config=quantization_config,
        device_map="auto"
    )
    
    processor = AutoProcessor.from_pretrained(args.model_path)
    processor.patch_size = 14
    
    # 设置eager attention
    model = force_eager_attention_with_decorator(model)
    
    # 加载问题
    print("Loading questions...")
    questions = [json.loads(q) for q in open(args.question_file, "r")]
    
    # 处理输出文件
    answers_file = args.answers_file
    os.makedirs(os.path.dirname(answers_file), exist_ok=True)
    
    # 检查已存在的结果（用于resume功能）
    answer_ids = []
    if args.resume and os.path.exists(answers_file):
        try:
            with open(answers_file, "r") as f:
                for line in f:
                    if line.strip():
                        answer_data = json.loads(line.strip())
                        answer_ids.append(answer_data['question_id'])
            print(f"Found {len(answer_ids)} existing results, will skip them.")
        except Exception as e:
            print(f"Warning: Error reading existing results file: {e}")
            answer_ids = []
    else:
        # 如果不是resume模式，清空文件
        if not args.resume:
            open(answers_file, 'w').close()
    
    # 统计关系类型
    relationship_stats = {}
    successful_count = 0
    
    # 处理每个问题
    print(f"Processing {len(questions)} questions with Visual Anchor Embeddings...")
    for q in tqdm(questions, desc="Processing Questions"):
        qid = q['id']
        if args.resume and qid in answer_ids:
            print(f"Skipping question {qid} (already processed)")
            continue
            
        print(f"\n{'='*50}")
        print(f"Processing question {qid}: {q['text']}")
        
        try:
            outputs, loss, rel_type, initial_loss = optimize_visual_anchors_for_question(q, model, processor, device, args)
            
            # 更新关系统计
            relationship_stats[rel_type] = relationship_stats.get(rel_type, 0) + 1
            
            # 立即写入单个问题的结果
            result = {
                "question_id": qid,
                "answers": outputs,  # [original, optimized]
                "relevancy": [[0, 0], [0, 0]],  # placeholder
                "label": q['name'],
                "final_loss": float(loss) if isinstance(loss, torch.Tensor) else loss,
                "relationship_type": rel_type,
                "anchor_config": {
                    "spatial": args.n_spatial_anchors,
                    "semantic": args.n_semantic_anchors,
                    "relational": args.n_relational_anchors
                },
                "initial_loss": float(initial_loss) if isinstance(initial_loss, torch.Tensor) else initial_loss
            }
            
            # 每处理完一个问题立即写入文件
            try:
                with open(answers_file, "a", encoding='utf-8') as ans_file:
                    ans_file.write(json.dumps(result, ensure_ascii=False) + '\n')
                    ans_file.flush()
                print(f"✓ Results saved for question {qid}")
                successful_count += 1
            except Exception as write_error:
                print(f"✗ Error writing results for question {qid}: {write_error}")
            
        except Exception as e:
            print(f"✗ Error processing question {qid}: {e}")
            import traceback
            if args.verbose:
                traceback.print_exc()
            continue
    
    # 打印统计信息
    print(f"\n{'='*50}")
    print("Processing Summary:")
    print(f"  Total questions: {len(questions)}")
    print(f"  Successfully processed: {successful_count}")
    print(f"  Skipped (resume): {len(answer_ids) if args.resume else 0}")
    print(f"  Failed: {len(questions) - successful_count - (len(answer_ids) if args.resume else 0)}")
    print(f"\nRelationship Statistics:")
    for rel_type, count in relationship_stats.items():
        print(f"  {rel_type}: {count}")
    
    print(f"\nResults saved to: {answers_file}")
    print("Visual Anchor Embeddings optimization completed!")


if __name__ == "__main__":
    main() 